//
//  AppSettings.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import Foundation

// MARK: - 用户设置数据模型
struct UserSetting: Codable, Equatable {
    var userId: String = "0224"
    var nickname: String = "新用户"
    var avatarData: Data? = nil
    var firstUsageDate: Date?
    var appUsageDays: Int = 0 // 初始化的日期
    var selectedAppIcon: String = "AppIcon" // 默认图标名称
    var geminiApiKey: String = "" // Gemini API密钥

    // MARK: - 头像管理

    /// 是否有自定义头像
    var hasCustomAvatar: Bool {
        avatarData != nil
    }

    /// 设置为系统默认头像
    mutating func setSystemDefaultAvatar() {
        self.avatarData = nil
    }

    /// 从 UIImage 设置自定义头像（自动压缩）
    /// - Parameter image: 传入的 UIImage，nil 表示清除
    mutating func setCustomAvatar(from image: UIImage?) {
        guard let image = image else {
            self.avatarData = nil
            return
        }

        // 压缩图像到合理尺寸（如 300x300）
        let maxSize: CGFloat = 300.0
        let resized = image.resized(to: CGSize(width: maxSize, height: maxSize))
        self.avatarData = resized.jpegData(compressionQuality: 0.8)
        
    }

    /// 获取头像 UIImage
    func getAvatarImage() -> UIImage? {
        if let data = avatarData {
            return UIImage(data: data)
        }
        return nil
    }

    /// 获取 SwiftUI Image（带 fallback）
    func getAvatarSwiftUIImage() -> Image {
        if let uiImage = getAvatarImage() {
            return Image(uiImage: uiImage)
        }
        return Image(systemName: "person.circle.fill")
    }
}

// MARK: - UIImage 扩展（用于压缩）
private extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        UIGraphicsImageRenderer(size: size).image { _ in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
    }
}

// MARK: - App设置和数据管理
@MainActor
final class AppSettings: ObservableObject {

    /// 使用 @AppStorage 存储编码后的 UserSetting 数据
    @AppStorage("userSettingsData") private var _userSettingsData: Data?

    /// 用于记录最后一次使用日期
    @AppStorage("lastUsageDate") private var lastUsageDate: Date?

    /// 获取当前登录用户ID
    @AppStorage("currentUserId") private var currentUserId: String = ""

    /// 实际被视图绑定的设置实例
    @Published var settings: UserSetting = UserSetting(){
        didSet{
            if let encoded = try? JSONEncoder().encode(settings) {
                _userSettingsData = encoded
            }
            // 当设置更新时，同步到云端
            Task {
                await syncUserInfoToCloud()
            }
        }
    }

    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    private let userInfoService = UserInfoUpdateService.shared

    init() {
        loadSettings()
        updateUsageDays()
        setupNotificationObservers()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: 统一保存方法
    func save() {
        if let encoded = try? encoder.encode(settings) {
            _userSettingsData = encoded
        }
    }
    
    func updateSettings(_ update: (inout UserSetting) -> Void) {
        var newSettings = settings
        update(&newSettings)
        settings = newSettings // 触发 didSet
    }
    
    // MARK: 使用计算属性便捷访问
    var nickname: String {
        get { settings.nickname }
        set {
            updateSettings { $0.nickname = newValue }
            // 昵称更新时同步到云端
            Task {
                await syncNicknameToCloud(newValue)
            }
        }
    }

    var userId: String {
        get { settings.userId }
        set { updateSettings { $0.userId = newValue } }
    }

    var geminiApiKey: String {
        get { settings.geminiApiKey }
        set { updateSettings { $0.geminiApiKey = newValue } }
    }

    // MARK: - 头像管理（带云端同步）

    /// 设置自定义头像并同步到云端
    func setCustomAvatarWithSync(from image: UIImage?) async {
        // 先更新本地设置
        updateSettings { settings in
            settings.setCustomAvatar(from: image)
        }

        // 如果有图片，上传到云端并更新用户信息
        if let image = image,
           let imageData = image.jpegData(compressionQuality: 0.8),
           !currentUserId.isEmpty {
            do {
                _ = try await userInfoService.uploadAvatarAndUpdateUser(
                    userId: currentUserId,
                    imageData: imageData
                )
                print("✅ 头像已同步到云端")
            } catch {
                print("❌ 头像同步到云端失败: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - 私有方法
    private func loadSettings() {
        guard let data = _userSettingsData,
              let decoded = try? decoder.decode(UserSetting.self, from: data)
        else { return }
        self.settings = decoded
    }

    // MARK: - 云端同步方法

    /// 同步用户信息到云端
    private func syncUserInfoToCloud() async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过云端同步")
            return
        }

        do {
            _ = try await userInfoService.updateUserInfo(
                userId: currentUserId,
                nickname: settings.nickname
            )
            print("✅ 用户信息已同步到云端")
        } catch {
            print("❌ 用户信息同步到云端失败: \(error.localizedDescription)")
        }
    }

    /// 同步昵称到云端
    private func syncNicknameToCloud(_ nickname: String) async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过昵称同步")
            return
        }

        do {
            _ = try await userInfoService.updateNickname(
                userId: currentUserId,
                nickname: nickname
            )
            print("✅ 昵称已同步到云端: \(nickname)")
        } catch {
            print("❌ 昵称同步到云端失败: \(error.localizedDescription)")
        }
    }

    /// 从云端加载用户信息
    func loadUserInfoFromCloud() async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过云端加载")
            return
        }

        do {
            let userData = try await userInfoService.getUserInfo(userId: currentUserId)

            // 更新本地设置（不触发云端同步）
            await MainActor.run {
                var newSettings = settings
                newSettings.nickname = userData.nickname
                newSettings.userId = userData.userId

                // 直接更新设置，避免触发didSet中的云端同步
                if let encoded = try? encoder.encode(newSettings) {
                    _userSettingsData = encoded
                    settings = newSettings
                }
            }

            // 如果有头像URL，下载并保存到本地
            if let avatarURL = userData.avatarURL, !avatarURL.isEmpty {
                await downloadAndSaveAvatar(from: avatarURL)
            }

            print("✅ 用户信息已从云端加载: \(userData.nickname)")
        } catch {
            print("❌ 从云端加载用户信息失败: \(error.localizedDescription)")
        }
    }

    /// 从URL下载头像并保存到本地沙盒
    private func downloadAndSaveAvatar(from urlString: String) async {
        guard let url = URL(string: urlString) else {
            print("❌ 无效的头像URL: \(urlString)")
            return
        }

        // 检查是否需要下载（避免重复下载相同的头像）
        let lastAvatarURL = UserDefaults.standard.string(forKey: "lastAvatarURL")
        if lastAvatarURL == urlString && settings.hasCustomAvatar {
            print("ℹ️ 头像URL未变化且本地已有头像，跳过下载")
            return
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查响应状态
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let image = UIImage(data: data) {

                // 在主线程更新UI
                await MainActor.run {
                    var newSettings = settings
                    newSettings.setCustomAvatar(from: image)

                    // 直接更新设置，避免触发didSet中的云端同步
                    if let encoded = try? encoder.encode(newSettings) {
                        _userSettingsData = encoded
                        settings = newSettings
                    }

                    // 保存当前头像URL，避免重复下载
                    UserDefaults.standard.set(urlString, forKey: "lastAvatarURL")
                }

                print("✅ 头像已从云端下载并保存到本地")
            } else {
                print("❌ 头像下载失败，HTTP状态码异常")
            }
        } catch {
            print("❌ 头像下载失败: \(error.localizedDescription)")
        }
    }

    private func updateUsageDays() {
        let now = Date()
        let calendar = Calendar.current

        guard let firstDate = settings.firstUsageDate else {
            // 初始化：记录今天为第一天，并将总天数设为 1
            updateSettings { settings in
                settings.firstUsageDate = now
                settings.appUsageDays = 1
            }
            return
            }

        let daysDifference = calendar.dateComponents([.day], from: firstDate, to: now).day ?? 0

        settings.appUsageDays = daysDifference + 1

        updateSettings { settings in
            settings.appUsageDays = daysDifference + 1
        }
    }



    // MARK: - 公共同步方法

    /// 手动触发完整的用户信息同步
    func syncAllUserInfoToCloud() async {
        await syncUserInfoToCloud()
    }

    /// 在用户登录后调用，从云端加载用户信息
    func onUserLogin(userId: String) async {
        currentUserId = userId
        await loadUserInfoFromCloud()
    }

    /// 在用户登录后调用，直接使用已获取的用户数据
    func onUserLoginWithData(userId: String, userData: UserData) async {
        currentUserId = userId

        // 直接更新本地设置（不触发云端同步）
        await MainActor.run {
            var newSettings = settings
            newSettings.nickname = userData.nickname
            newSettings.userId = userData.userId

            // 直接更新设置，避免触发didSet中的云端同步
            if let encoded = try? encoder.encode(newSettings) {
                _userSettingsData = encoded
                settings = newSettings
            }
        }

        // 如果有头像URL，立即下载并保存到本地
        if let avatarURL = userData.avatarURL, !avatarURL.isEmpty {
            await downloadAndSaveAvatar(from: avatarURL)
        }
    }

    /// 在用户登出时调用，清理用户相关数据
    func onUserLogout() {
        currentUserId = ""
        // 清理头像URL缓存
        let defaults = UserDefaults.standard
        
        // 清除所有的本地数据
        defaults.dictionaryRepresentation().keys.forEach { key in
            defaults.removeObject(forKey: key)
        }
        defaults.synchronize() // 确认立即写入（非必须，但可确保同步）
        // 清理本地用户设置
        updateSettings { $0 = UserSetting() }
    }

    // MARK: - 通知监听设置

    /// 设置通知监听器
    private func setupNotificationObservers() {
        // 监听用户登录通知（基本版本）
        NotificationCenter.default.addObserver(
            forName: .userDidLogin,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let userId = userInfo["userId"] as? String else { return }

            Task {
                await self.onUserLogin(userId: userId)
            }
        }

        // 监听用户登录通知（携带用户数据版本）
        NotificationCenter.default.addObserver(
            forName: .userDidLoginWithData,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let userId = userInfo["userId"] as? String,
                  let userData = userInfo["userData"] as? UserData else { return }

            Task {
                await self.onUserLoginWithData(userId: userId, userData: userData)
            }
        }

        // 监听用户登出通知
        NotificationCenter.default.addObserver(
            forName: .userDidLogout,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            Task {
                await MainActor.run {
                    self.onUserLogout()
                }
            }
        }
    }
}
